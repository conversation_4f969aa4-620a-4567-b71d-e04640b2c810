import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

const API_BASE_URL = process.env['NEXT_PUBLIC_API_URL'] || '';

export async function GET(request: NextRequest) {
  try {
    // Get the search parameters
    const searchParams = request.nextUrl.searchParams;
    const projectId = searchParams.get('project_id');
    
    // Validate required parameters
    if (!projectId) {
      return NextResponse.json(
        { error: "project_id is required" },
        { status: 400 }
      );
    }

    console.log("API route received project_id:", projectId);

    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Build the backend API URL
    const backendUrl = new URL(
      `${process.env['BACKEND_URL'] || 'http://localhost:8000'}/knowledge-base`
    );
    
    // Copy all query parameters to the backend URL
    searchParams.forEach((value, key) => {
      if (value) {
        backendUrl.searchParams.append(key, value);
      }
    });
    
    console.log('Forwarding request to:', backendUrl.toString());

    // Make the request to the backend API
    const response = await fetch(backendUrl.toString(), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token['accessToken']}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      let errorMessage = 'Failed to process request';
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.detail || errorMessage;
      } catch (e) {
        const errorText = await response.text();
        console.error(`Backend API error (${response.status}):`, errorText);
      }
      
      // Handle specific error cases
      if (response.status === 401) {
        return NextResponse.json(
          { error: 'Authentication failed. Please log in again.' },
          { status: 401 }
        );
      }
      
      if (response.status === 403) {
        return NextResponse.json(
          { error: 'You do not have permission to access this project.' },
          { status: 403 }
        );
      }

      return NextResponse.json(
        { error: errorMessage },
        { status: response.status }
      );
    }

    // Return the response from the backend
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Knowledge base API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();

    // Get the authentication token
    const token = await getToken({ req: request as any });
    if (!token || !token['accessToken']) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }
    // Build the backend API URL
    const backendUrl = new URL(
      `${process.env['BACKEND_URL'] || 'http://localhost:8000'}/knowledge-base`
    );
    
    // Make the request to the backend API
    const response = await fetch(backendUrl.toString(), {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token['accessToken']}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(await request.json())
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Backend API error (${response.status}):`, errorText);

      return NextResponse.json(
        { error: `Failed to create knowledge entry: ${response.status} ${errorText}` },
        { status: response.status }
      );
    }

    // Return the response from the backend
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Knowledge base API error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}