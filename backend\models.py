from sqlalchemy import (Column, Integer, String, Text, ForeignKey, Enum, TIMESTAMP, func, DateTime, Boolean, Table, JSON)
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property
from datetime import datetime
import enum

from .database import Base

class CategoryEnum(str, enum.Enum):
    EXCEPTIONS = "EXCEPTIONS"
    COMMON_ISSUES = "COMMON_ISSUES"
    MISC = "MISC"
    IGNORED_EXCEPTIONS = "IGNORED_EXCEPTIONS"

class RoleEnum(str, enum.Enum):
    ADMIN = "ADMIN"
    USER = "USER"

class SystemNameEnum(str, enum.Enum):
    OMS = "OMS"
    WMS = "WMS"
    AUTOMATION = "AUTOMATION"
    OTHERS = "OTHERS"

class AccessLevelEnum(str, enum.Enum):
    READ = "READ"
    WRITE = "WRITE"
    ADMIN = "ADMIN"

# admin.py
class Admin(Base):
    __tablename__ = "admins"

    admin_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), unique=True, nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now())

    # Relationship to User (one-to-one)
    user = relationship("User", back_populates="admin")

# user.py
class User(Base):
    __tablename__ = "users"

    user_id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    email = Column(String(255), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now())
    
    def __repr__(self):
        return f"<User {self.name}>"

    # Only keep ONE relationship to Admin
    admin = relationship("Admin", back_populates="user", uselist=False)

    # Hybrid property for admin check
    @hybrid_property
    def is_admin(self):
        # Check if the admin attribute is already loaded to avoid DetachedInstanceError
        if 'admin' in self.__dict__:
            return self.admin is not None
        # If not loaded and we're in a detached state, we can't determine admin status
        # Return False as a safe default
        return False

    # Relationships
    created_issues = relationship("Issue", back_populates="creator", foreign_keys="Issue.created_by")
    reviewed_issues = relationship("Issue", back_populates="reviewer", foreign_keys="Issue.reviewed_by")
    solutions = relationship("Solution", back_populates="provider")
    solution_history = relationship("SolutionHistory", back_populates="updater")
    issue_files = relationship("IssueFile", back_populates="uploader")
    knowledge_base = relationship("KnowledgeBase", back_populates="author")
    project_roles = relationship("UserProjectRole", back_populates="user")
    favorites = relationship("UserFavorite", back_populates="user")
    knowledge_favorites = relationship("KnowledgeBaseFavorite", back_populates="user")
    project_access = relationship("ProjectAccess", back_populates="user")

class Project(Base):
    __tablename__ = "projects"

    project_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    project_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    icon_name = Column(String(100), nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now())

    # Relationships
    issues = relationship("Issue", back_populates="project")
    knowledge_base = relationship("KnowledgeBase", back_populates="project")
    user_roles = relationship("UserProjectRole", back_populates="project")
    user_access = relationship("ProjectAccess", back_populates="project")

class UserProjectRole(Base):
    __tablename__ = "user_project_roles"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    project_id = Column(Integer, ForeignKey("projects.project_id", ondelete="CASCADE"), nullable=False)
    role = Column(Enum('ADMIN', 'USER'), nullable=False)
    assigned_at = Column(TIMESTAMP, server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="project_roles")
    project = relationship("Project", back_populates="user_roles")

class Issue(Base):
    __tablename__ = "issues"

    issue_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey("projects.project_id", ondelete="CASCADE"), nullable=False)
    category = Column(Enum('EXCEPTIONS', 'COMMON_ISSUES', 'MISC', 'IGNORED_EXCEPTIONS'), nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    created_by = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now())
    error_code = Column(String(50))
    error_message = Column(String(255))
    issue_type = Column(String(100))
    status = Column(Enum('Open', 'In Progress', 'Resolved', 'Closed'), default='Open')
    impact = Column(Enum('Low', 'Medium', 'High', 'Critical'))
    frequency = Column(Enum('Rare', 'Occasional', 'Frequent', 'Always'))
    jira_id = Column(String(50))
    jira_link = Column(String(255))
    hemants_view = Column(Text)
    system_name = Column(Enum(SystemNameEnum))
    reviewed_by = Column(Integer, ForeignKey("users.user_id", ondelete="SET NULL"))
    reviewed_at = Column(TIMESTAMP)

    # Relationships
    project = relationship("Project", back_populates="issues")
    creator = relationship("User", back_populates="created_issues", foreign_keys=[created_by])
    reviewer = relationship("User", back_populates="reviewed_issues", foreign_keys=[reviewed_by])
    solutions = relationship("Solution", back_populates="issue")
    files = relationship("IssueFile", back_populates="issue")

class Solution(Base):
    __tablename__ = "solutions"

    solution_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    issue_id = Column(Integer, ForeignKey("issues.issue_id", ondelete="CASCADE"), nullable=False)
    category = Column(Enum(CategoryEnum), nullable=False)
    provided_by = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    solution_text = Column(Text, nullable=False)
    upvotes = Column(Integer, default=0)
    created_at = Column(TIMESTAMP, server_default=func.now())

    # Relationships
    issue = relationship("Issue", back_populates="solutions")
    provider = relationship("User", back_populates="solutions")
    history = relationship("SolutionHistory", back_populates="solution")
    # Update back_populates to match KnowledgeBase.solution_obj
    knowledge_base_entries = relationship("KnowledgeBase", back_populates="solution_obj")
    
    def __init__(self, **kwargs):
        # Ensure category is properly converted to CategoryEnum if it's a string
        if 'category' in kwargs and isinstance(kwargs['category'], str):
            kwargs['category'] = CategoryEnum(kwargs['category'].upper())
        super().__init__(**kwargs)

class SolutionHistory(Base):
    __tablename__ = "solution_history"

    history_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    solution_id = Column(Integer, ForeignKey("solutions.solution_id", ondelete="CASCADE"), nullable=False)
    previous_text = Column(Text, nullable=False)
    updated_by = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    updated_at = Column(TIMESTAMP, server_default=func.now())

    # Relationships
    solution = relationship("Solution", back_populates="history")
    updater = relationship("User", back_populates="solution_history")

class IssueFile(Base):
    __tablename__ = "issue_files"

    file_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    issue_id = Column(Integer, ForeignKey("issues.issue_id", ondelete="CASCADE"), nullable=False)
    file_url = Column(String(500), nullable=False)
    uploaded_by = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    uploaded_at = Column(TIMESTAMP, server_default=func.now())

    # Relationships
    issue = relationship("Issue", back_populates="files")
    uploader = relationship("User", back_populates="issue_files")

class KnowledgeBase(Base):
    __tablename__ = "knowledge_base"

    kb_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    project_id = Column(Integer, ForeignKey("projects.project_id", ondelete="CASCADE"), nullable=False)
    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)
    solution_id = Column(Integer, ForeignKey("solutions.solution_id", ondelete="SET NULL"), nullable=True)
    uploaded_by = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    file_url = Column(String(500))
    created_at = Column(TIMESTAMP, server_default=func.now())
    solution = Column(Text, nullable=True)

    # Relationships
    project = relationship("Project", back_populates="knowledge_base")
    author = relationship("User", back_populates="knowledge_base")
    favorites = relationship("KnowledgeBaseFavorite", back_populates="knowledge_base")
    # Rename the relationship to avoid conflict
    solution_obj = relationship("Solution", back_populates="knowledge_base_entries")

class UserFavorite(Base):
    __tablename__ = "user_favorites"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    issue_id = Column(Integer, ForeignKey("issues.issue_id", ondelete="CASCADE"), nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="favorites")
    issue = relationship("Issue", backref="favorited_by")

class KnowledgeBaseFavorite(Base):
    __tablename__ = "knowledge_base_favorites"

    favorite_id = Column(Integer, primary_key=True, index=True)
    kb_id = Column(Integer, ForeignKey("knowledge_base.kb_id", ondelete="CASCADE"))
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    created_at = Column(TIMESTAMP, server_default=func.now())

    # Relationships
    knowledge_base = relationship("KnowledgeBase", back_populates="favorites")
    user = relationship("User", back_populates="knowledge_favorites")

class ProjectAccess(Base):
    __tablename__ = "project_access"

    access_id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    project_id = Column(Integer, ForeignKey("projects.project_id", ondelete="CASCADE"), nullable=False)
    access_level = Column(Enum(AccessLevelEnum), nullable=False, default=AccessLevelEnum.READ)
    granted_at = Column(TIMESTAMP, server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="project_access")
    project = relationship("Project", back_populates="user_access")
