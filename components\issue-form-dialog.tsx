'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Session } from 'next-auth';
import { useToast } from '@/components/ui/use-toast';
import { SystemNameEnum, CategoryEnum, StatusEnum, ImpactEnum, FrequencyEnum } from '@/lib/api';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogClose, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

interface IssueBaseData {
  project_id: number;
  category: CategoryEnum;
  title: string;
  error_code: string | null;
  error_message: string | null;
  system_name: SystemNameEnum | null;
  issue_type: string | null;
  status: StatusEnum;
  impact: ImpactEnum | null;
  frequency: FrequencyEnum | null;
  description: string;
  jira_id: string | null;
  jira_link: string | null;
  hemants_view: string | null;
}

interface IssueData extends IssueBaseData {
  issue_id?: number; 
  solution?: SolutionData | null;
  created_at?: string;
  updated_at?: string;
  created_by?: number;
  reviewed_by?: number | null;
  reviewed_at?: string | null;
}

interface SolutionData {
  issue_id: number;
  category: CategoryEnum;
  solution_text: string;
  provided_by: number;
  verified: boolean;
}

interface IssueFormDialogProps {
  issue?: IssueData;
  open: boolean;
  onOpenChange?: (open: boolean) => void;
  onOpenChangeAction?: (open: boolean) => void; // For backward compatibility
  onSubmit?: (data: { issue: IssueData; solution?: SolutionData | null }) => Promise<void>;
  onSubmitAction?: (data: { issue: IssueData; solution?: SolutionData | null }) => Promise<void>; // For backward compatibility
  mode: 'create' | 'edit';
  projectId: number;
}

// Extended session type to include accessToken in user object
interface ExtendedSession extends Session {
  user: {
    accessToken: string;
  } & Session['user'];
}

export function IssueFormDialog({ 
  issue, 
  open, 
  onOpenChange, 
  onOpenChangeAction, 
  onSubmit,
  onSubmitAction,     
  mode, 
  projectId 
}: IssueFormDialogProps) {
  const [activeTab, setActiveTab] = useState('details');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data: session } = useSession() as { data: ExtendedSession | null };
  const { toast } = useToast();

  // Form state
  const [formData, setFormData] = useState<IssueBaseData>({
    title: issue?.title || '',
    category: issue?.category || CategoryEnum.CommonIssues,
    error_code: issue?.error_code || '',
    error_message: issue?.error_message || '',
    system_name: issue?.system_name || null,
    issue_type: issue?.issue_type || null,
    status: issue?.status || StatusEnum.Open,
    impact: issue?.impact || null,
    frequency: issue?.frequency || null,
    description: issue?.description || '',
    jira_id: issue?.jira_id || '',
    jira_link: issue?.jira_link || '',
    hemants_view: issue?.hemants_view || '',
    project_id: projectId
  });
  
  // Initialize solution state
  const [solution, setSolution] = useState({
    solution_text: issue?.solution?.solution_text || '',
    verified: issue?.solution?.verified || false
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isSubmitting) return;
    
    const startTime = performance.now();
    setIsSubmitting(true);
    let success = false;

    try {
      // Validate required fields
      if (!formData.title?.trim()) {
        throw new Error('Title is required');
      }
      if (!formData.description?.trim()) {
        throw new Error('Description is required');
      }

      // Prepare the issue data with proper typing
      const issueData: IssueData = {
        ...formData,
        project_id: projectId,
        issue_id: issue?.issue_id ?? 0, // Provide default 0 for new issues
        status: formData.status || 'open',
        created_at: issue?.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        // Convert empty strings to null for optional fields
        error_code: formData.error_code?.trim() || null,
        error_message: formData.error_message?.trim() || null,
        jira_id: formData.jira_id?.trim() || null,
        jira_link: formData.jira_link?.trim() || null,
        hemants_view: formData.hemants_view?.trim() || null,
        // Handle enum fields - convert empty strings to null
        system_name: formData.system_name || null,
        issue_type: formData.issue_type?.trim() || null,
        impact: formData.impact || null,
        frequency: formData.frequency || null,
      };

      // Prepare solution data if it exists
      const solutionData: SolutionData | null = solution.solution_text ? {
        issue_id: issue?.issue_id || 0,
        category: formData.category,
        solution_text: solution.solution_text,
        provided_by: session?.user?.id ? Number(session.user.id) : 0,
        verified: solution.verified
      } : null;

      // Use onSubmit if provided, otherwise use onSubmitAction for backward compatibility
      const submitHandler = onSubmit || onSubmitAction;
      if (!submitHandler) {
        throw new Error('No submit handler provided');
      }

      // Call the submit handler with proper typing
      await submitHandler({
        issue: issueData,
        solution: solutionData,
      });
      
      success = true;
      
      // Show success message with action details
      toast({
        title: 'Success',
        description: `Issue ${mode === 'create' ? 'created' : 'updated'} successfully`,
        duration: 3000,
      });
      
      // Close the dialog on success
      const openChangeHandler = onOpenChange || onOpenChangeAction;
      if (openChangeHandler) {
        openChangeHandler(false);
      }
      
      // Show success message
      toast({
        title: 'Success',
        description: `Issue ${mode === 'create' ? 'created' : 'updated'} successfully`,
      });                                                                                        
    } catch (error) {
      console.error('Error submitting issue:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit issue. stry again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (isOpen: boolean) => {
    if (isSubmitting) return;
    
    // Use onOpenChange if provided, otherwise use onOpenChangeAction
    if (onOpenChange) {
      onOpenChange(isOpen);
    } else if (onOpenChangeAction) {
      onOpenChangeAction(isOpen);
    } else {
      console.warn('No onOpenChange or onOpenChangeAction handler provided to IssueFormDialog');
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent 
        className="max-w-4xl max-h-[93vh] flex flex-col overflow-hidden"
        onInteractOutside={(e) => {
          if (isSubmitting) {
            e.preventDefault();
          }
        }}
      >
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Create New Issue' : 'Edit Issue'}
          </DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="flex-1 flex flex-col overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col overflow-hidden">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="details">Issue Details</TabsTrigger>
              <TabsTrigger value="solution">Solution</TabsTrigger>
            </TabsList>
            
            <div className="flex-1 overflow-y-auto p-4">
              <TabsContent value="details" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Title *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData({...formData, title: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="error_code">Error Code</Label>
                    <Input
                      id="error_code"
                      value={formData.error_code || ''}
                      onChange={(e) => setFormData({...formData, error_code: e.target.value})}
                      placeholder="Error Code"
                    />
                  </div>
                                                                                   
                  <div className="space-y-2">
                    <Label htmlFor="category">Category *</Label>
                    <Select
                      value={formData.category}
                      onValueChange={(value) => setFormData({...formData, category: value as CategoryEnum})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(CategoryEnum).map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">Status *</Label>
                    <Select
                      value={formData.status}
                      onValueChange={(value) => setFormData({...formData, status: value as StatusEnum})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(StatusEnum).map((status) => (
                          <SelectItem key={status} value={status}>
                            {status}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="impact">Impact</Label>
                    <Select
                      value={formData.impact || ''}
                      onValueChange={(value) => setFormData({...formData, impact: value as ImpactEnum})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select impact" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(ImpactEnum).map((impact) => (
                          <SelectItem key={impact} value={impact}>
                            {impact}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="frequency">Frequency</Label>
                    <Select
                      value={formData.frequency || ''}
                      onValueChange={(value) => setFormData({...formData, frequency: value as FrequencyEnum})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(FrequencyEnum).map((freq) => (
                          <SelectItem key={freq} value={freq}>
                            {freq}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="system_name">System</Label>
                    <Select
                      value={formData.system_name || ''}
                      onValueChange={(value) => setFormData({...formData, system_name: value as SystemNameEnum})}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select system" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(SystemNameEnum).map((system) => (
                          <SelectItem key={system} value={system}>
                            {system}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="description">Description *</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      rows={4}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="error_message">Error Message</Label>
                    <Textarea
                      id="error_message"
                      value={formData.error_message || ''}
                      onChange={(e) => setFormData({...formData, error_message: e.target.value})}
                      rows={2}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="jira_id">Jira ID</Label>
                    <Input
                      id="jira_id"
                      value={formData.jira_id || ''}
                      onChange={(e) => setFormData({...formData, jira_id: e.target.value})}
                      placeholder="e.g., PROJ-123"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="jira_link">Jira Link</Label>
                    <Input
                      id="jira_link"
                      value={formData.jira_link || ''}
                      onChange={(e) => setFormData({...formData, jira_link: e.target.value})}
                      placeholder="https://jira.example.com/browse/PROJ-123"
                      type="url"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="hemants_view">Client's Feedback</Label>
                    <Textarea
                      id="hemants_view"
                      value={formData.hemants_view || ''}
                      onChange={(e) => setFormData({...formData, hemants_view: e.target.value})}
                      rows={2}
                      placeholder="Client's perspective or feedback on the issue"
                      className="min-h-[80px]"
                    />
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="solution" className="space-y-4">
                <div className="space-y-2">
                  <Label>Solution Details</Label>
                  <Textarea
                    value={solution.solution_text}
                    onChange={(e) => setSolution({...solution, solution_text: e.target.value})}
                    placeholder="Describe the solution to this issue..."
                    rows={6}
                    className="min-h-[150px]"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="verified" 
                    checked={solution.verified}
                    onCheckedChange={(checked) => setSolution({...solution, verified: checked as boolean})}
                  />
                  <Label htmlFor="verified">Mark as verified</Label>
                </div>
              </TabsContent>
            </div>
          </Tabs>
          
          <div className="flex justify-end space-x-2 p-4 border-t">
            <DialogClose asChild>
              <Button
                type="button"
                variant="outline"
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Submitting...' : mode === 'create' ? 'Create Issue' : 'Update Issue'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
