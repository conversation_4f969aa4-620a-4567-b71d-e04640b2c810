import { useState, useEffect, useMemo, useCallback } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { StatusBadge } from "@/components/ui/status-badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Star, AlertCircle, Calendar, Search } from "lucide-react";
import { favoritesApi, ApiError, issueApi, SystemNameEnum, StatusEnum, CategoryEnum } from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";
import { IssueDetailsDialog } from "@/components/issue-details-dialog";
import { useRouter } from "next/navigation";
import { getStatusColor, getImpactColor, getFrequencyInfo, getSystemInfo, formatDate, PendingOperationsState, FavoriteState, getStatusInfo } from "@/lib/utils/issue-utils";

// Types
interface IssuesListMappedIssue extends Omit<MappedIssue, 'comments'> {
  comments?: number;
}

interface IssuesListProps {
  filter: string;
  category: string;
  issues: IssuesListMappedIssue[];
  onRefresh: () => Promise<void>;
}

export interface MappedIssue {
  id: number;
  title: string;
  error_code?: string;
  error_message?: string;
  system_name: SystemNameEnum;
  issue_type?: string;
  status: StatusEnum;
  impact?: string;
  frequency?: string;
  category?: CategoryEnum;
  description: string;
  createdBy: string;
  createdAt: string;
  jira_id?: string | null;
  jira_link?: string | null;
  hemants_view?: string | null;
  solution: {
    exists: boolean;
    content?: string;
    verified?: boolean;
    author?: string;
    createdAt?: string;
  };
}

// --- Custom Hooks ---
function useFavorites(filter: string) {
  const [favorites, setFavorites] = useState<FavoriteState>({});
  const [isLoadingFavorites, setIsLoadingFavorites] = useState(true);
  const { toast } = useToast();

  // Fetch favorites
  const fetchFavorites = useCallback(async () => {
    try {
      const response = await favoritesApi.getUserFavorites();
      const favoritesMap: FavoriteState = {};
      response?.items?.forEach((favorite) => {
        if (favorite?.issue_id) favoritesMap[favorite.issue_id] = true;
      });
      setFavorites(favoritesMap);
      localStorage.setItem("userFavorites", JSON.stringify(favoritesMap));
    } catch (error) {
      const stored = localStorage.getItem("userFavorites");
      if (stored) setFavorites(JSON.parse(stored));
    } finally {
      setIsLoadingFavorites(false);
    }
  }, []);

  useEffect(() => { fetchFavorites(); }, [fetchFavorites]);
  useEffect(() => { if (filter === "favorites") fetchFavorites(); }, [filter, fetchFavorites]);

  // Toggle favorite
  const toggleFavorite = useCallback(async (issueId: number) => {
    const isFavorite = !favorites[issueId];
    const newFavorites = { ...favorites, [issueId]: isFavorite };
    setFavorites(newFavorites);
    localStorage.setItem("userFavorites", JSON.stringify(newFavorites));
    try {
      if (isFavorite) await favoritesApi.addFavorite(issueId);
      else await favoritesApi.removeFavorite(issueId);
      if (filter === "favorites") fetchFavorites();
      toast({ title: isFavorite ? "Added to favorites" : "Removed from favorites", description: `Issue #${issueId} has been ${isFavorite ? "added to" : "removed from"} your favorites.`, duration: 3000 });
    } catch (error) {
      setFavorites(favorites); // revert
      toast({ title: "Error", description: "Failed to update favorites. Please try again.", variant: "destructive" });
    }
  }, [favorites, filter, fetchFavorites, toast]);

  return { favorites, isLoadingFavorites, toggleFavorite };
}

function useFilteredIssues(issues: IssuesListMappedIssue[], filter: string, category: string, favorites: FavoriteState, searchQuery: string) {
  return useMemo(() => {
    return issues
      .filter(issue => {
        if (searchQuery) {
          const searchLower = searchQuery.toLowerCase();
          const matchesSearch = issue.title.toLowerCase().includes(searchLower) || issue.description.toLowerCase().includes(searchLower) || issue.error_code?.toLowerCase().includes(searchLower) || issue.error_message?.toLowerCase().includes(searchLower);
          if (!matchesSearch) return false;
        }
        if (filter === "favorites" && !favorites[issue.id]) return false;
        if (category !== "all" && issue?.category) {
          const normalizedCategory = typeof category === 'string' ? category.replace(/-/g, '_').toUpperCase() : '';
          const issueCategory = issue.category && typeof issue.category === 'string' ? issue.category.toUpperCase() : '';
          if (category.toLowerCase() === "ignored-exception" || (typeof category === 'string' && category.toLowerCase().includes('ignored'))) {
            return (issueCategory && (issueCategory.includes('IGNORED') || issueCategory.includes('EXCEPTION') || issueCategory === 'IGNORED_EXCEPTION')) || false;
          }
          if (normalizedCategory && issueCategory) {
            if (!issueCategory.includes(normalizedCategory) && !normalizedCategory.includes(issueCategory.replace('_', ''))) return false;
          } else return false;
        }
        return true;
      })
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [issues, filter, category, favorites, searchQuery]);
}

// --- Card Subcomponent ---
function IssueCard({ issue, isFavorite, onToggleFavorite, onOpenDetails }: { issue: MappedIssue, isFavorite: boolean, onToggleFavorite: (id: number) => void, onOpenDetails: (issue: MappedIssue) => void }) {
  // Fallback classes for impact, frequency, and system
  const impactClass = getImpactColor(issue.impact || '') || "bg-gray-200";
  const frequencyInfo = getFrequencyInfo(issue.frequency || '');
  const frequencyBg = frequencyInfo?.bgColor || "bg-gray-200";
  const frequencyColor = frequencyInfo?.color || "text-gray-700";
  const frequencyIcon = frequencyInfo?.icon || null;
  const systemInfo = getSystemInfo(issue.system_name);
  const systemBg = systemInfo?.bgColor || "bg-gray-200";
  const systemColor = systemInfo?.color || "text-gray-700";
  const systemIcon = systemInfo?.icon || null;

  return (
    <Card key={issue.id} className="overflow-hidden h-full flex flex-col hover:shadow-md transition-shadow cursor-pointer border border-transparent hover:border-primary/20" onClick={() => onOpenDetails(issue)}>
      <CardHeader className="pb-2 flex-shrink-0">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <CardTitle className="text-base line-clamp-1">{issue.title}</CardTitle>
            <CardDescription className="mt-1 text-xs">
              {issue.error_code && <span className="font-mono bg-muted px-1 py-0.5 rounded mr-1">{issue.error_code}</span>}
              <Badge variant="outline" className="text-xs">#{issue.id}</Badge>
            </CardDescription>
          </div>
          <Button variant="ghost" size="icon" onClick={e => { e.stopPropagation(); onToggleFavorite(issue.id); }} className="h-7 w-7">
            <Star className={`h-4 w-4 ${isFavorite ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pb-2 flex-grow">
        <div className="flex flex-wrap gap-1 mb-2">
          <StatusBadge status={issue.status} statusInfo={getStatusInfo(issue.status)} className="text-xs" />
          {issue.impact && <Badge className={`${impactClass} text-white text-xs`}>{issue.impact}</Badge>}
          {issue.frequency && issue.frequency !== 'UNKNOWN' && <Badge className={`${frequencyBg} ${frequencyColor} text-xs`}>{frequencyIcon}{issue.frequency}</Badge>}
          {issue.system_name && issue.system_name !== 'OTHERS' && <Badge className={`${systemBg} ${systemColor} text-xs`}>{systemIcon}{issue.system_name}</Badge>}
        </div>
        <p className="text-xs line-clamp-3 text-muted-foreground">{issue.description}</p>
      </CardContent>
      <CardFooter className="pt-0 pb-3 flex justify-between items-center text-xs text-muted-foreground flex-shrink-0">
        <div className="flex items-center gap-1"><Calendar className="h-3 w-3" /><span>{formatDate(issue.createdAt)}</span></div>
        {issue.solution && issue.solution.exists && <Badge variant="outline" className="text-xs bg-green-50">Solution</Badge>}
      </CardFooter>
    </Card>
  );
}

// --- Main Component ---
export function IssuesList({ filter, category, issues, onRefresh }: IssuesListProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [selectedIssue, setSelectedIssue] = useState<MappedIssue | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const { favorites, isLoadingFavorites, toggleFavorite } = useFavorites(filter);
  const filteredIssues = useFilteredIssues(issues, filter, category, favorites, searchQuery);
  const [isDialogLoading, setIsDialogLoading] = useState(false);

  // Helper to map backend issue to MappedIssue
  const mapToMappedIssue = (issue: any): MappedIssue => {
    return {
      id: issue.issue_id || issue.id,
      title: issue.title || '',
      error_code: issue.error_code || '',
      error_message: issue.error_message || '',
      system_name: issue.system_name || SystemNameEnum.OTHERS,
      issue_type: issue.issue_type || '',
      status: issue.status || StatusEnum.Open,
      impact: issue.impact || '',
      frequency: issue.frequency || '',
      category: issue.category || CategoryEnum.CommonIssues,
      description: issue.description || '',
      createdBy: issue.created_by ? String(issue.created_by) : 'Unknown',
      createdAt: issue.created_at || new Date().toISOString(),
      jira_id: issue.jira_id || null,
      jira_link: issue.jira_link || null,
      hemants_view: issue.hemants_view || null,
      solution: issue.solution || { exists: false },
    };
  };

  // Open issue details dialog (fetch full details)
  const openIssueDetails = useCallback(async (issue: MappedIssue) => {
    setIsDialogLoading(true);
    setIsDetailsDialogOpen(true);
    try {
      const fullIssue = await issueApi.getIssue(issue.id);
      const mapped = mapToMappedIssue(fullIssue);
      mapped.solution = mapped.solution?.exists ? mapped.solution : issue.solution;
      setSelectedIssue(mapped);
    } catch (error) {
      toast({ title: "Warning", description: "Could not load full issue details. Showing summary.", variant: "destructive" });
      setSelectedIssue(issue); // fallback to summary
    } finally {
      setIsDialogLoading(false);
    }
  }, [toast]);

  // Render loading skeleton take on me
  if (isLoadingFavorites) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="overflow-hidden">
            <CardHeader className="pb-2"><div className="flex justify-between"><Skeleton className="h-6 w-3/4" /><Skeleton className="h-6 w-6 rounded-full" /></div></CardHeader>
            <CardContent><Skeleton className="h-4 w-full mb-2" /><Skeleton className="h-4 w-3/4" /></CardContent>
            <CardFooter className="flex justify-between pt-2"><Skeleton className="h-4 w-1/4" /><Skeleton className="h-4 w-1/4" /></CardFooter>
          </Card>
        ))}
      </div>
    );
  }

  // Render empty state
  if (filteredIssues.length === 0) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">No issues found</h3>
        <p className="text-muted-foreground mt-2">{filter === "favorites" ? "You haven't added any issues to your favorites yet." : "No issues match the current filter criteria."}</p>
        <div className="flex justify-center gap-2 mt-4">
          {filter === "favorites" && <Button variant="outline" onClick={() => router.push("/issues?tab=all")}>View all issues</Button>}
          <Button variant="outline" onClick={async () => { setSearchQuery(''); await onRefresh(); toast({ title: "Refreshed", description: "Issues list has been refreshed." }); }}>Refresh</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center space-x-2 w-full max-w-md">
          <div className="relative w-full">
            <input type="text" placeholder="Search issues..." className="w-full px-3 py-2 border rounded-md pl-10" value={searchQuery} onChange={e => setSearchQuery(e.target.value)} />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {filteredIssues.map((issue) => (
          <IssueCard key={issue.id} issue={issue} isFavorite={!!favorites[issue.id]} onToggleFavorite={toggleFavorite} onOpenDetails={openIssueDetails} />
        ))}
      </div>
      <IssueDetailsDialog 
        issue={selectedIssue} 
        open={isDetailsDialogOpen} 
        onOpenChange={setIsDetailsDialogOpen} 
        onToggleFavorite={toggleFavorite} 
        isFavorite={selectedIssue ? !!favorites[selectedIssue.id] : false}
        isLoading={isDialogLoading}
      />
    </div>
  );
}