import { api } from './client';
import { KnowledgeBase, PaginatedResponse, SearchFilters } from './types';

// Using just the endpoint path since API_BASE_URL already includes /api
const KNOWLEDGE_BASE = '/knowledge-base';

export const knowledgeBaseApi = {
  /**
   * Get all knowledge base entries with pagination and filters
   */
  getKnowledgeBase: (filters: SearchFilters = {}) => {
    const params = new URLSearchParams();

    if (filters.query) params.append('search', filters.query); // Backend expects 'search' not 'query'
    if (filters.project_id) params.append('project_id', filters.project_id.toString());
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());
    if (filters.sort_by) params.append('sort_by', filters.sort_by);
    if (filters.sort_order) params.append('sort_order', filters.sort_order);

    console.log('Knowledge base API params:', params.toString());

    return api.get<PaginatedResponse<KnowledgeBase>>(
      `${KNOWLEDGE_BASE}?${params.toString()}`,
      {
        transformResponse: async (response: Response) => {
          const data = await response.json();
          // Normalize the response to always have items, total, page, limit, total_pages
          const limit = data.limit || data.per_page || filters.limit || 1000;
          return {
            items: data.items || [],
            total: data.total || 0,
            page: data.page || 1,
            limit,
            total_pages: data.total_pages || (data.total && limit ? Math.ceil(data.total / Number(limit)) : 1) || 1,
            data: data.data || null,
          };
        }
      }
    );
  },

  /**
   * Search knowledge base entries
   */
  searchKnowledgeBase: (query: string, filters: Omit<SearchFilters, 'query'> = {}) =>
    knowledgeBaseApi.getKnowledgeBase({ ...filters, query }),

  /**
   * Get knowledge base entry by ID
   */
  getKnowledgeBaseEntry: (kbId: number) =>
    api.get<KnowledgeBase>(`${KNOWLEDGE_BASE}/${kbId}`),

  /**
   * Create a new knowledge base entry
   */
  createKnowledgeBaseEntry: (data: Omit<KnowledgeBase, 'kb_id' | 'created_at' | 'uploaded_by'>) =>
    api.post<KnowledgeBase>(KNOWLEDGE_BASE, data),

  /**
   * Update a knowledge base entry
   */
  updateKnowledgeBaseEntry: (kbId: number, data: Partial<KnowledgeBase>) =>
    api.put<KnowledgeBase>(`${KNOWLEDGE_BASE}/${kbId}`, data),

  /**
   * Delete a knowledge base entry
   */
  deleteKnowledgeBaseEntry: (kbId: number) =>
    api.delete<{ success: boolean }>(`${KNOWLEDGE_BASE}/${kbId}`),

  /**
   * Toggle like on a knowledge base entry
   */
  toggleLike: (kbId: number) =>
    api.post<{ success: boolean; likes: number; is_liked: boolean }>(
      `${KNOWLEDGE_BASE}/${kbId}/like`
    ),

  /**
   * Toggle favorite on a knowledge base entry
   */
  toggleFavorite: (kbId: number) =>
    api.post<{ success: boolean; is_favorite: boolean }>(
      `${KNOWLEDGE_BASE}/${kbId}/favorite`
    ),

  /**
   * Get user's favorite knowledge base entries
   */
  getFavorites: (params?: { page?: number; limit?: number }) =>
    api.get<PaginatedResponse<KnowledgeBase>>(
      `${KNOWLEDGE_BASE}/favorites`,
      { params: params as Record<string, any> }
    ),

  /**
   * Get knowledge base entries by tag
   */
  getByTag: (tag: string, params?: { page?: number; limit?: number }) =>
    api.get<PaginatedResponse<KnowledgeBase>>(
      `${KNOWLEDGE_BASE}/tags/${encodeURIComponent(tag)}`,
      { params: params as Record<string, any> }
    ),

  likeKnowledgeEntry: (kbId: number) =>
    api.post<{ success: boolean; likes: number; is_liked: boolean }>(`${KNOWLEDGE_BASE}/${kbId}/like`),

  toggleFavoriteKnowledge: (kbId: number) =>
    api.post<{ success: boolean; is_favorite: boolean }>(`${KNOWLEDGE_BASE}/${kbId}/favorite`),
};
