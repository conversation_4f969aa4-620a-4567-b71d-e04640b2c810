import { StatusEnum, SystemNameEnum } from "@/lib/api/types";

export interface Issue {
  id: number;
  title: string;
  error_code: string;
  error_message: string;
  system_name: SystemNameEnum;
  issue_type: string;
  status: StatusEnum;
  impact: string;
  frequency: string;
  category: string;
  description: string;
  created_by: string;
  created_at: string;
  jira_id?: string | number | null;
  jira_link?: string | number | null;
  hemants_view?: string | null;
  solution: {
    exists: boolean;
    content?: string;
    verified?: boolean;
    author?: string;
    created_at?: string;
  };
}

// Unified MappedIssue interface for consistent data structure across components
export interface MappedIssue {
  id: number;
  title: string;
  error_code?: string;
  error_message?: string;
  system_name: SystemNameEnum | string; // Allow both enum and string for flexibility
  issue_type?: string; // Consistent field name matching backend
  status: StatusEnum | string; // Allow both enum and string for flexibility
  impact?: string;
  frequency?: string;
  category?: string;
  description: string;
  createdBy: string;
  createdAt: string;
  jira_id?: string | number | null;
  jira_link?: string | number | null;
  hemants_view?: string | null;
  solution: {
    exists: boolean;
    content?: string;
    verified?: boolean;
    author?: string;
    createdAt?: string;
  };
}

export interface IssueDetailsDialogProps {
  issue: MappedIssue | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onToggleFavoriteAction: (issueId: number) => Promise<void>;
  isFavorite: boolean;
}

// Re-export StatusInfo from utils for consistency
export type { StatusInfo } from "@/lib/utils/issue-utils";